"""
Test script for Dynamic Booking System - Agent V2
Demonstrates the step-by-step booking process with dynamic requirements
"""

import logging
import sys
import os

# Add parent directory to path to import utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent_v2.main_agent import MainAgentV2

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_dynamic_booking_system():
    """Test the dynamic booking system with step-by-step collection"""
    
    print("🎯 Testing Dynamic Booking System - Agent V2")
    print("=" * 60)
    
    try:
        # Initialize the agent
        agent = MainAgentV2()
        print("✅ Agent V2 initialized successfully")
        
        # Test booking conversation
        thread_id = "booking_test_user_1"
        
        print(f"\n💬 Starting booking conversation (Thread: {thread_id})")
        print("-" * 50)
        
        # Test 1: Start booking process
        print("\n👤 User: I want to book an appointment")
        response = agent.chat("I want to book an appointment", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 2: Provide name
        print("\n👤 User: My name is <PERSON>")
        response = agent.chat("My name is <PERSON>", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 3: Provide course/product
        print("\n👤 User: I'm interested in IELTS preparation course")
        response = agent.chat("I'm interested in IELTS preparation course", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 4: Provide email
        print("\n👤 User: <EMAIL>")
        response = agent.chat("<EMAIL>", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 5: Provide phone
        print("\n👤 User: +977-9841234567")
        response = agent.chat("+977-9841234567", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 6: Select time slot
        print("\n👤 User: I'll take slot number 3")
        response = agent.chat("I'll take slot number 3", thread_id)
        print(f"🤖 Agent: {response}")
        
        print("\n" + "="*60)
        print("🧪 Testing Booking Updates")
        print("-" * 50)
        
        # Test new conversation - existing booking
        thread_id_2 = "booking_test_user_2"
        
        # Test 7: Start new booking
        print(f"\n👤 User (New Thread): I want to book")
        response = agent.chat("I want to book", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        # Test 8: Provide name
        print("\n👤 User: John Doe")
        response = agent.chat("John Doe", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        # Test 9: Provide course
        print("\n👤 User: German language course")
        response = agent.chat("German language course", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        # Test 10: Invalid email
        print("\n👤 User: invalid-email")
        response = agent.chat("invalid-email", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        # Test 11: Valid email
        print("\n👤 User: <EMAIL>")
        response = agent.chat("<EMAIL>", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        # Test 12: Invalid phone
        print("\n👤 User: 123")
        response = agent.chat("123", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        # Test 13: Valid phone
        print("\n👤 User: 9876543210")
        response = agent.chat("9876543210", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        print("\n" + "="*60)
        print("🔄 Testing Booking Updates")
        print("-" * 50)
        
        # Test 14: Try to continue first booking (should show existing)
        print(f"\n👤 User (Back to Thread 1): I want to book again")
        response = agent.chat("I want to book again", thread_id)
        print(f"🤖 Agent: {response}")
        
        print("\n✅ Dynamic Booking System testing completed!")
        print("\n🎯 Key features demonstrated:")
        print("   • ✅ Step-by-step information collection")
        print("   • ✅ Dynamic field validation (email, phone)")
        print("   • ✅ Progress tracking and summaries")
        print("   • ✅ Time slot selection after info collection")
        print("   • ✅ Booking confirmation with details")
        print("   • ✅ Session management per thread")
        print("   • ✅ Existing booking detection")
        print("   • ✅ Error handling for invalid inputs")
        print("   • ✅ No hardcoded booking flow!")
        
    except Exception as e:
        logger.error(f"❌ Error testing booking system: {e}")
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    test_dynamic_booking_system()
