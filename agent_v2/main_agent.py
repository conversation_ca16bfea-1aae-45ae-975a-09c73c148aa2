"""
Main Agent V2 - Modern LangChain implementation using LangGraph
Uses LangGraph's built-in persistence and memory management patterns
"""

import os
import logging
from typing import Literal, Dict, Any
from dotenv import load_dotenv

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage
from langgraph.checkpoint.memory import MemorySaver

from utils import log_user_input, log_agent_response, log_tool_call, log_tool_result

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# Global booking agent instance to maintain session state
_global_booking_agent = None

def get_booking_agent():
    """Get the global booking agent instance"""
    global _global_booking_agent
    if _global_booking_agent is None:
        from .booking_agent import BookingAgentV2
        _global_booking_agent = BookingAgentV2()
    return _global_booking_agent


@tool
def search_information(user_message: str) -> str:
    """
    Search for general information, troubleshooting, apps, and services.
    
    Args:
        user_message: The user's exact message for information search
        
    Returns:
        Search results for information/troubleshooting queries
    """
    log_tool_call("search_information", f"user_message='{user_message}'")
    
    try:
        from .search_agent import SearchAgentV2
        search_agent = SearchAgentV2()
        result = search_agent.search_information(user_message)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error in information search: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool  
def search_products(user_message: str) -> str:
    """
    Search for products, courses, and educational programs.
    
    Args:
        user_message: The user's exact message for product search
        
    Returns:
        Search results for courses/products
    """
    log_tool_call("search_products", f"user_message='{user_message}'")
    
    try:
        from .search_agent import SearchAgentV2
        search_agent = SearchAgentV2()
        result = search_agent.search_products(user_message)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error in product search: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def start_booking(thread_id: str = "default") -> str:
    """
    Start a new booking process or continue existing one.

    Args:
        thread_id: Thread identifier for the booking session

    Returns:
        Next step in the booking process
    """
    log_tool_call("start_booking", f"thread_id='{thread_id}'")

    try:
        from .booking_agent import BookingAgentV2
        booking_agent = BookingAgentV2()
        result = booking_agent.start_booking_process(thread_id)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error starting booking: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def collect_booking_info(thread_id: str, field_name: str, value: str) -> str:
    """
    Collect booking information step by step.

    Args:
        thread_id: Thread identifier
        field_name: Field being collected (name, course_product, email, phone)
        value: Value provided by user

    Returns:
        Next step or confirmation
    """
    log_tool_call("collect_booking_info", f"thread_id='{thread_id}', field='{field_name}', value='{value}'")

    try:
        from .booking_agent import BookingAgentV2
        booking_agent = BookingAgentV2()
        result = booking_agent.collect_booking_info(thread_id, field_name, value)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error collecting booking info: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def select_timeslot(thread_id: str, slot_number: str) -> str:
    """
    Select a time slot for booking.

    Args:
        thread_id: Thread identifier
        slot_number: Selected slot number

    Returns:
        Booking confirmation
    """
    log_tool_call("select_timeslot", f"thread_id='{thread_id}', slot='{slot_number}'")

    try:
        from .booking_agent import BookingAgentV2
        booking_agent = BookingAgentV2()
        result = booking_agent.select_timeslot(thread_id, slot_number)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error selecting timeslot: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def update_booking_info(thread_id: str, field_name: str, new_value: str) -> str:
    """
    Update existing booking information.

    Args:
        thread_id: Thread identifier
        field_name: Field to update
        new_value: New value

    Returns:
        Update confirmation
    """
    log_tool_call("update_booking_info", f"thread_id='{thread_id}', field='{field_name}', value='{new_value}'")

    try:
        from .booking_agent import BookingAgentV2
        booking_agent = BookingAgentV2()
        result = booking_agent.update_booking_info(thread_id, field_name, new_value)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error updating booking: {str(e)}"
        logger.error(error_msg)
        return error_msg


# Define tools list
tools = [search_information, search_products, start_booking, collect_booking_info, select_timeslot, update_booking_info]

# Create memory for conversation persistence using LangGraph's built-in patterns
memory = MemorySaver()

MAIN_AGENT_PROMPT = """You are a professional, friendly sales representative for a customer service center. You help customers find solutions and courses that meet their needs.

You have access to these tools:
- search_information: For troubleshooting, app issues, technical problems, general information
- search_products: For courses, programs, educational products, "what courses" questions
- start_booking: Start a new booking process or continue existing one
- collect_booking_info: Collect booking information (name, course_product, email, phone)
- select_timeslot: Select a time slot after all info is collected
- update_booking_info: Update existing booking information

BOOKING WORKFLOW:
1. When user wants to book → use start_booking with thread_id
2. System will ask for required info step by step (name, course/product, email, phone)
3. For each piece of info → use collect_booking_info with field_name and value
4. After all info collected → system shows available time slots
5. When user selects slot → use select_timeslot with slot_number
6. To update info → use update_booking_info

IMPORTANT RULES:
1. For questions about courses/education/products → use search_products
2. For questions about apps/troubleshooting/problems → use search_information
3. For booking requests → start with start_booking, then follow the workflow
4. For simple greetings (hello, hi, namaste) → respond directly
5. Always pass the thread_id to booking tools for session management

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both. Respond in the same language style as the user.

Be helpful, engaging, and solution-focused. Guide users through the booking process step by step."""


class MainAgentV2:
    """
    Main Agent V2 - Modern LangChain implementation using LangGraph
    Uses LangGraph's built-in persistence and memory management
    """

    def __init__(self):
        """Initialize the agent using the official LangChain pattern"""
        self.memory = memory
        self.llm = main_llm
        self.tools = tools

        # Use the official LangChain pattern from the documentation
        from langgraph.prebuilt import create_react_agent
        from langchain_core.prompts import ChatPromptTemplate

        # Create a proper prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", MAIN_AGENT_PROMPT),
            ("placeholder", "{messages}"),
        ])

        self.agent = create_react_agent(
            model=self.llm,
            tools=self.tools,
            checkpointer=self.memory,
            prompt=prompt
        )
        logger.info("✅ Main Agent V2 initialized with official LangGraph pattern")

    
    def chat(self, message: str, thread_id: str = "default") -> str:
        """
        Process a user message and return the response using LangGraph
        
        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management
            
        Returns:
            Agent's response
        """
        log_user_input(message)
        
        # Configure thread for memory persistence
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # Invoke the agent with the message
            response = self.agent.invoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )
            
            # Extract the final response
            final_response = response["messages"][-1].content
            log_agent_response(final_response)
            
            # Log tool usage for debugging
            tool_calls_made = []
            for msg in response["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_calls_made.append(tool_call['name'])
            
            if tool_calls_made:
                logger.info(f"🔧 TOOLS USED: {tool_calls_made}")
            else:
                logger.warning("⚠️  NO TOOLS USED - Possible hallucination!")
            
            return final_response
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def stream_chat(self, message: str, thread_id: str = "default"):
        """
        Stream the agent's response for real-time interaction
        
        Args:
            message: User's message
            thread_id: Conversation thread ID
            
        Yields:
            Streaming response chunks
        """
        log_user_input(message)
        
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            for step in self.agent.stream(
                {"messages": [HumanMessage(content=message)]},
                config=config,
                stream_mode="values"
            ):
                yield step["messages"][-1]
                
        except Exception as e:
            error_msg = f"Error in streaming: {str(e)}"
            logger.error(error_msg)
            yield error_msg
    
    def get_conversation_history(self, thread_id: str = "default") -> list:
        """
        Get conversation history for a thread using LangGraph's memory
        
        Args:
            thread_id: Conversation thread ID
            
        Returns:
            List of messages in the conversation
        """
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # Get the current state which includes message history
            state = self.agent.get_state(config)
            return state.values.get("messages", [])
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
