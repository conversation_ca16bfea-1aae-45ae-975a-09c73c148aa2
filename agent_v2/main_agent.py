"""
Main Agent V2 - Modern LangChain implementation using LangGraph
Uses LangGraph's built-in persistence and memory management patterns
"""

import os
import logging
from typing import Literal, Dict, Any
from dotenv import load_dotenv

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, MessagesState, StateGraph
from langgraph.prebuilt import create_react_agent

from utils import log_user_input, log_agent_response, log_tool_call, log_tool_result

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)


@tool
def search_information(user_message: str) -> str:
    """
    Search for general information, troubleshooting, apps, and services.
    
    Args:
        user_message: The user's exact message for information search
        
    Returns:
        Search results for information/troubleshooting queries
    """
    log_tool_call("search_information", f"user_message='{user_message}'")
    
    try:
        from .search_agent import SearchAgentV2
        search_agent = SearchAgentV2()
        result = search_agent.search_information(user_message)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error in information search: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool  
def search_products(user_message: str) -> str:
    """
    Search for products, courses, and educational programs.
    
    Args:
        user_message: The user's exact message for product search
        
    Returns:
        Search results for courses/products
    """
    log_tool_call("search_products", f"user_message='{user_message}'")
    
    try:
        from .search_agent import SearchAgentV2
        search_agent = SearchAgentV2()
        result = search_agent.search_products(user_message)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error in product search: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def book_appointment(action: str, **kwargs) -> str:
    """
    Handle appointment-related tasks.
    
    Args:
        action: The booking action - "get_slots", "book_appointment", "get_date"
        **kwargs: Additional parameters for the booking action
        
    Returns:
        Result from the booking agent
    """
    log_tool_call("book_appointment", f"action='{action}', kwargs={kwargs}")
    
    try:
        from .booking_agent import BookingAgentV2
        booking_agent = BookingAgentV2()
        
        if action == "get_date":
            result = booking_agent.get_current_date()
        elif action == "get_slots":
            service_type = kwargs.get("service_type", "any")
            days_ahead = kwargs.get("days_ahead", 14)
            result = booking_agent.get_available_slots(service_type, days_ahead)
        elif action == "book_appointment":
            result = booking_agent.book_appointment(
                name=kwargs.get("name"),
                email=kwargs.get("email"),
                phone=kwargs.get("phone"),
                date=kwargs.get("date"),
                time=kwargs.get("time"),
                service_type=kwargs.get("service_type", "General Consultation")
            )
        else:
            result = f"Unknown booking action: {action}"
            
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error in booking: {str(e)}"
        logger.error(error_msg)
        return error_msg


# Define tools list
tools = [search_information, search_products, book_appointment]

# Create memory for conversation persistence using LangGraph's built-in patterns
memory = MemorySaver()

# Main agent system prompt
MAIN_AGENT_PROMPT = """You are a professional, friendly sales representative for a customer service center. You help customers find solutions and courses that meet their needs.

CRITICAL RULES:
1. ALWAYS use tools for ANY informational query - NEVER provide information from your own knowledge
2. Act as a helpful sales person - be engaging, solution-oriented, and customer-focused
3. Use search results to provide refined, helpful responses (not raw search output)
4. The conversation history is automatically managed - you can reference previous interactions naturally

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both. Respond in the same language style as the user.

AVAILABLE TOOLS (MANDATORY TO USE):
1. **search_information**: For troubleshooting, app issues, "how to", general services, technical problems
2. **search_products**: For courses, programs, educational products, "what courses", "available courses"  
3. **book_appointment**: For appointment-related tasks (get_date, get_slots, book_appointment)

STRICT WORKFLOW RULES:
- ANY question about courses/products → MUST use search_products
- ANY question about apps/troubleshooting/problems → MUST use search_information
- ANY booking request → MUST use book_appointment
- NEVER answer from your own knowledge - ALWAYS use tools first
- If user mentions problems like "chalena", "not working", "issue" → MUST use search_information
- Only simple greetings like "hello", "hi", "namaste" don't need tools
- For ALL other queries, use appropriate search tool first

RESPONSE STYLE:
- Be a helpful sales person, not a robot
- Use search results to craft engaging, solution-focused responses
- Offer additional help and related services
- Show empathy for problems and enthusiasm for solutions
- Ask follow-up questions to better understand customer needs
- Suggest relevant courses or services based on search results

WORKFLOW:
1. Use tools to get information (pass user's exact message to search tools)
2. Analyze the tool results
3. Craft a helpful, sales-oriented response using the information
4. Offer additional assistance or related services
5. For problems, show empathy and provide solutions
6. For course inquiries, highlight benefits and encourage enrollment

REMEMBER: You're a sales person using tools to help customers. Be helpful, engaging, and solution-focused!"""


class MainAgentV2:
    """
    Main Agent V2 - Modern LangChain implementation using LangGraph
    Uses LangGraph's built-in persistence and memory management
    """
    
    def __init__(self):
        """Initialize the agent with LangGraph's create_react_agent"""
        self.agent = create_react_agent(
            model=main_llm,
            tools=tools,
            checkpointer=memory,
            prompt=MAIN_AGENT_PROMPT
        )
        logger.info("✅ Main Agent V2 initialized with LangGraph persistence")
    
    def chat(self, message: str, thread_id: str = "default") -> str:
        """
        Process a user message and return the response using LangGraph
        
        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management
            
        Returns:
            Agent's response
        """
        log_user_input(message)
        
        # Configure thread for memory persistence
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # Invoke the agent with the message
            response = self.agent.invoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )
            
            # Extract the final response
            final_response = response["messages"][-1].content
            log_agent_response(final_response)
            
            # Log tool usage for debugging
            tool_calls_made = []
            for msg in response["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_calls_made.append(tool_call['name'])
            
            if tool_calls_made:
                logger.info(f"🔧 TOOLS USED: {tool_calls_made}")
            else:
                logger.warning("⚠️  NO TOOLS USED - Possible hallucination!")
            
            return final_response
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def stream_chat(self, message: str, thread_id: str = "default"):
        """
        Stream the agent's response for real-time interaction
        
        Args:
            message: User's message
            thread_id: Conversation thread ID
            
        Yields:
            Streaming response chunks
        """
        log_user_input(message)
        
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            for step in self.agent.stream(
                {"messages": [HumanMessage(content=message)]},
                config=config,
                stream_mode="values"
            ):
                yield step["messages"][-1]
                
        except Exception as e:
            error_msg = f"Error in streaming: {str(e)}"
            logger.error(error_msg)
            yield error_msg
    
    def get_conversation_history(self, thread_id: str = "default") -> list:
        """
        Get conversation history for a thread using LangGraph's memory
        
        Args:
            thread_id: Conversation thread ID
            
        Returns:
            List of messages in the conversation
        """
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # Get the current state which includes message history
            state = self.agent.get_state(config)
            return state.values.get("messages", [])
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
