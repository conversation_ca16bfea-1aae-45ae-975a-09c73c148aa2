"""
Memory Manager - Modern LangChain memory management patterns
Uses LangChain's built-in memory management instead of hardcoded context handling
"""

import logging
from typing import Dict, List, Any, Optional
from langchain.memory import ConversationSummaryBufferMemory, ConversationBufferWindowMemory
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain_google_genai import Cha<PERSON><PERSON><PERSON>gleGenerativeAI
from langchain.memory.chat_message_histories import Chat<PERSON><PERSON>age<PERSON>istory
from langchain.schema.memory import BaseChatMessageHistory
import os
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


class ConversationMemoryManager:
    """
    Manages conversation memory using LangChain's built-in patterns
    Replaces hardcoded context handling with proper memory management
    """
    
    def __init__(self, memory_type: str = "buffer_window", max_token_limit: int = 2000):
        """
        Initialize memory manager with LangChain memory patterns
        
        Args:
            memory_type: Type of memory ("buffer_window", "summary_buffer", "entity")
            max_token_limit: Maximum tokens to keep in memory
        """
        self.memory_type = memory_type
        self.max_token_limit = max_token_limit
        
        # Initialize LLM for memory operations
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        
        # Store memories per thread/session
        self.memories: Dict[str, Any] = {}
        
        logger.info(f"✅ Memory Manager initialized with {memory_type} memory")
    
    def get_memory(self, thread_id: str = "default") -> Any:
        """
        Get or create memory for a specific thread
        
        Args:
            thread_id: Conversation thread identifier
            
        Returns:
            LangChain memory instance
        """
        if thread_id not in self.memories:
            self.memories[thread_id] = self._create_memory()
            logger.info(f"🧠 Created new memory for thread: {thread_id}")
        
        return self.memories[thread_id]
    
    def _create_memory(self) -> Any:
        """Create appropriate memory type based on configuration"""
        
        if self.memory_type == "buffer_window":
            # Keep last N messages in memory
            return ConversationBufferWindowMemory(
                k=10,  # Keep last 10 messages
                return_messages=True,
                memory_key="chat_history"
            )
        
        elif self.memory_type == "summary_buffer":
            # Summarize old messages, keep recent ones
            return ConversationSummaryBufferMemory(
                llm=self.llm,
                max_token_limit=self.max_token_limit,
                return_messages=True,
                memory_key="chat_history"
            )
        
        else:
            # Default to buffer window
            return ConversationBufferWindowMemory(
                k=10,
                return_messages=True,
                memory_key="chat_history"
            )
    
    def add_message(self, thread_id: str, human_message: str, ai_message: str):
        """
        Add a conversation turn to memory
        
        Args:
            thread_id: Conversation thread identifier
            human_message: User's message
            ai_message: AI's response
        """
        memory = self.get_memory(thread_id)
        
        # Add messages to memory
        memory.chat_memory.add_user_message(human_message)
        memory.chat_memory.add_ai_message(ai_message)
        
        logger.debug(f"💭 Added conversation turn to memory for thread: {thread_id}")
    
    def get_context(self, thread_id: str) -> str:
        """
        Get conversation context for the thread
        
        Args:
            thread_id: Conversation thread identifier
            
        Returns:
            Formatted conversation context
        """
        memory = self.get_memory(thread_id)
        
        try:
            # Get memory variables (includes chat history)
            memory_vars = memory.load_memory_variables({})
            chat_history = memory_vars.get("chat_history", [])
            
            if not chat_history:
                return "No previous conversation context."
            
            # Format context from chat history
            context_parts = []
            for message in chat_history[-5:]:  # Last 5 messages for context
                if isinstance(message, HumanMessage):
                    context_parts.append(f"User: {message.content}")
                elif isinstance(message, AIMessage):
                    context_parts.append(f"Assistant: {message.content}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting context: {e}")
            return "Error retrieving conversation context."
    
    def analyze_intent(self, thread_id: str, current_message: str) -> Dict[str, Any]:
        """
        Analyze user intent based on conversation history and current message
        Uses LangChain memory to understand context
        
        Args:
            thread_id: Conversation thread identifier
            current_message: Current user message
            
        Returns:
            Intent analysis dictionary
        """
        memory = self.get_memory(thread_id)
        context = self.get_context(thread_id)
        
        # Analyze intent based on context and current message
        intent_analysis = {
            "search_type": "information",  # Default
            "is_problem": False,
            "mentioned_entities": [],
            "user_intent": "general_inquiry",
            "context_aware_query": current_message
        }
        
        current_lower = current_message.lower()
        context_lower = context.lower()
        
        # Detect problem context from conversation history
        problem_keywords = ["chalena", "not working", "problem", "issue", "error", "dikka"]
        intent_analysis["is_problem"] = any(keyword in context_lower or keyword in current_lower 
                                          for keyword in problem_keywords)
        
        # Entity extraction from context
        if "ambition guru" in context_lower or "ambition" in context_lower:
            intent_analysis["mentioned_entities"].append("Ambition Guru")
        
        if "ielts" in context_lower or "ielts" in current_lower:
            intent_analysis["mentioned_entities"].append("IELTS")
        
        # Intent classification
        if any(word in current_lower for word in ["book", "booking", "join", "class"]):
            intent_analysis["user_intent"] = "booking"
        elif any(word in current_lower for word in ["course", "kun kun", "what courses"]):
            intent_analysis["user_intent"] = "course_inquiry"
            intent_analysis["search_type"] = "products"
        elif intent_analysis["is_problem"]:
            intent_analysis["user_intent"] = "troubleshooting"
            intent_analysis["search_type"] = "information"
        
        # Create context-aware query
        if intent_analysis["is_problem"] and "Ambition Guru" in intent_analysis["mentioned_entities"]:
            intent_analysis["context_aware_query"] = f"Ambition Guru app troubleshooting {current_message}"
        elif intent_analysis["user_intent"] == "course_inquiry":
            intent_analysis["context_aware_query"] = f"available courses programs {current_message}"
        
        logger.info(f"🎯 Intent analysis: {intent_analysis['user_intent']} | "
                   f"Search type: {intent_analysis['search_type']} | "
                   f"Problem: {intent_analysis['is_problem']}")
        
        return intent_analysis
    
    def clear_memory(self, thread_id: str):
        """Clear memory for a specific thread"""
        if thread_id in self.memories:
            del self.memories[thread_id]
            logger.info(f"🗑️ Cleared memory for thread: {thread_id}")
    
    def get_memory_stats(self, thread_id: str) -> Dict[str, Any]:
        """Get memory statistics for debugging"""
        memory = self.get_memory(thread_id)
        
        try:
            memory_vars = memory.load_memory_variables({})
            chat_history = memory_vars.get("chat_history", [])
            
            return {
                "thread_id": thread_id,
                "memory_type": self.memory_type,
                "message_count": len(chat_history),
                "last_message": chat_history[-1].content if chat_history else None
            }
        except Exception as e:
            return {
                "thread_id": thread_id,
                "error": str(e)
            }
