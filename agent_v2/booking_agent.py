"""
Booking Agent V2 - Modern LangChain implementation
Handles appointment booking with proper error handling and validation
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import random

# Setup logging
logger = logging.getLogger(__name__)


class BookingAgentV2:
    """
    Booking Agent V2 - Handles appointment scheduling and management
    Modern implementation with proper validation and error handling
    """
    
    def __init__(self):
        """Initialize the booking agent"""
        # Mock database for available slots
        self.available_slots = self._generate_mock_slots()
        self.booked_appointments = []
        logger.info("✅ Booking Agent V2 initialized")
    
    def _generate_mock_slots(self) -> List[Dict]:
        """Generate mock available time slots for the next 30 days"""
        slots = []
        start_date = datetime.now().date()
        
        # Generate slots for next 30 days
        for day_offset in range(1, 31):
            date = start_date + timedelta(days=day_offset)
            
            # Skip weekends for this example
            if date.weekday() >= 5:
                continue
            
            # Generate time slots (9 AM to 5 PM)
            for hour in range(9, 17):
                # Randomly make some slots unavailable
                if random.random() > 0.3:  # 70% availability
                    slots.append({
                        "date": date.strftime("%Y-%m-%d"),
                        "time": f"{hour:02d}:00",
                        "service_type": "General Consultation",
                        "available": True
                    })
                    
                    # Add some specialized slots
                    if random.random() > 0.7:  # 30% chance
                        slots.append({
                            "date": date.strftime("%Y-%m-%d"),
                            "time": f"{hour:02d}:30",
                            "service_type": "IELTS Consultation",
                            "available": True
                        })
        
        return slots
    
    def get_current_date(self) -> str:
        """
        Get the current date and time
        
        Returns:
            Formatted current date and time
        """
        now = datetime.now()
        formatted_date = now.strftime("%A, %B %d, %Y at %I:%M %p")
        
        logger.info(f"📅 Current date requested: {formatted_date}")
        
        return f"Today is {formatted_date}"
    
    def get_available_slots(self, service_type: str = "any", days_ahead: int = 14) -> str:
        """
        Get available appointment slots
        
        Args:
            service_type: Type of service ("any", "General Consultation", "IELTS Consultation")
            days_ahead: Number of days ahead to search
            
        Returns:
            Formatted string of available slots
        """
        logger.info(f"📅 Getting available slots for {service_type}, {days_ahead} days ahead")
        
        try:
            # Filter slots based on criteria
            end_date = (datetime.now().date() + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
            
            filtered_slots = []
            for slot in self.available_slots:
                if not slot["available"]:
                    continue
                    
                if slot["date"] > end_date:
                    continue
                
                if service_type != "any" and slot["service_type"] != service_type:
                    continue
                
                filtered_slots.append(slot)
            
            if not filtered_slots:
                return f"No available slots found for {service_type} in the next {days_ahead} days. Please try a different service type or extend the search period."
            
            # Group by date
            slots_by_date = {}
            for slot in filtered_slots[:20]:  # Limit to first 20 slots
                date = slot["date"]
                if date not in slots_by_date:
                    slots_by_date[date] = []
                slots_by_date[date].append(slot)
            
            # Format response
            response_parts = [f"Available slots for {service_type}:\n"]
            
            for date, slots in sorted(slots_by_date.items()):
                # Convert date to readable format
                date_obj = datetime.strptime(date, "%Y-%m-%d")
                readable_date = date_obj.strftime("%A, %B %d, %Y")
                
                response_parts.append(f"\n📅 {readable_date}:")
                for slot in slots:
                    response_parts.append(f"  • {slot['time']} - {slot['service_type']}")
            
            response_parts.append(f"\nTo book an appointment, please provide your name, email, phone number, preferred date, and time.")
            
            return "".join(response_parts)
            
        except Exception as e:
            error_msg = f"Error retrieving available slots: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def book_appointment(self, name: str, email: str, phone: str, 
                        date: str, time: str, service_type: str = "General Consultation") -> str:
        """
        Book an appointment
        
        Args:
            name: Customer name
            email: Customer email
            phone: Customer phone number
            date: Appointment date (YYYY-MM-DD format)
            time: Appointment time (HH:MM format)
            service_type: Type of service
            
        Returns:
            Booking confirmation or error message
        """
        logger.info(f"📅 Booking appointment for {name} on {date} at {time}")
        
        try:
            # Validate required fields
            if not all([name, email, phone, date, time]):
                return "Please provide all required information: name, email, phone, date, and time."
            
            # Validate email format (basic)
            if "@" not in email or "." not in email:
                return "Please provide a valid email address."
            
            # Validate phone format (basic)
            if len(phone.replace("-", "").replace(" ", "")) < 10:
                return "Please provide a valid phone number."
            
            # Check if slot is available
            slot_found = False
            for slot in self.available_slots:
                if (slot["date"] == date and 
                    slot["time"] == time and 
                    slot["service_type"] == service_type and 
                    slot["available"]):
                    
                    # Mark slot as booked
                    slot["available"] = False
                    slot_found = True
                    break
            
            if not slot_found:
                return f"Sorry, the requested slot ({date} at {time} for {service_type}) is not available. Please check available slots and try again."
            
            # Create booking record
            booking = {
                "booking_id": f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}",
                "name": name,
                "email": email,
                "phone": phone,
                "date": date,
                "time": time,
                "service_type": service_type,
                "status": "confirmed",
                "created_at": datetime.now().isoformat()
            }
            
            self.booked_appointments.append(booking)
            
            # Format confirmation
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            readable_date = date_obj.strftime("%A, %B %d, %Y")
            
            confirmation = f"""
✅ Appointment Confirmed!

Booking Details:
• Booking ID: {booking['booking_id']}
• Name: {name}
• Email: {email}
• Phone: {phone}
• Date: {readable_date}
• Time: {time}
• Service: {service_type}

A confirmation email will be sent to {email}. Please arrive 10 minutes early for your appointment.

If you need to reschedule or cancel, please contact us with your booking ID: {booking['booking_id']}
"""
            
            logger.info(f"✅ Appointment booked successfully: {booking['booking_id']}")
            return confirmation.strip()
            
        except Exception as e:
            error_msg = f"Error booking appointment: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def get_booking_stats(self) -> Dict:
        """Get booking statistics for debugging"""
        total_slots = len(self.available_slots)
        available_slots = len([s for s in self.available_slots if s["available"]])
        booked_slots = total_slots - available_slots
        
        return {
            "total_slots": total_slots,
            "available_slots": available_slots,
            "booked_slots": booked_slots,
            "total_bookings": len(self.booked_appointments)
        }
