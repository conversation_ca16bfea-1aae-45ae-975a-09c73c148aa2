"""
Booking Agent V2 - Dynamic booking system with step-by-step collection
Handles appointment booking with dynamic requirements and booking updates
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import random

# Setup logging
logger = logging.getLogger(__name__)


class BookingAgentV2:
    """
    Booking Agent V2 - Dynamic booking system with step-by-step collection
    Handles appointment scheduling with dynamic requirements and updates
    """

    def __init__(self):
        """Initialize the booking agent with dynamic requirements"""
        # Mock database for available slots
        self.available_slots = self._generate_mock_slots()
        self.booked_appointments = []

        # Dynamic booking requirements - can be configured
        self.booking_requirements = {
            "name": {"required": True, "type": "string", "prompt": "your full name"},
            "course_product": {"required": True, "type": "string", "prompt": "the course/product you're interested in"},
            "email": {"required": True, "type": "email", "prompt": "your email address"},
            "phone": {"required": True, "type": "phone", "prompt": "your phone number"}
        }

        # Active booking sessions (thread_id -> booking_data)
        self.active_bookings = {}

        logger.info("✅ Booking Agent V2 initialized with dynamic requirements")

    def start_booking_process(self, thread_id: str = "default") -> str:
        """
        Start a new booking process or continue existing one

        Args:
            thread_id: Thread identifier for the booking session

        Returns:
            Next step message for the user
        """
        logger.info(f"📅 Starting booking process for thread: {thread_id}")

        # Check if there's an existing booking session
        if thread_id in self.active_bookings:
            booking_data = self.active_bookings[thread_id]
            if booking_data.get("status") == "completed":
                return self._show_existing_booking(thread_id)
            else:
                return self._continue_booking_process(thread_id)

        # Start new booking session
        self.active_bookings[thread_id] = {
            "status": "collecting_info",
            "collected_data": {},
            "created_at": datetime.now().isoformat()
        }

        return self._get_next_required_field(thread_id)

    def collect_booking_info(self, thread_id: str, field_name: str, value: str) -> str:
        """
        Collect booking information dynamically

        Args:
            thread_id: Thread identifier
            field_name: Name of the field being collected
            value: Value provided by user

        Returns:
            Response message
        """
        if thread_id not in self.active_bookings:
            return "No active booking session. Please start a new booking process."

        booking_data = self.active_bookings[thread_id]

        # Validate the field
        validation_result = self._validate_field(field_name, value)
        if not validation_result["valid"]:
            return f"❌ {validation_result['message']} Please provide {self.booking_requirements[field_name]['prompt']}."

        # Store the validated value
        booking_data["collected_data"][field_name] = validation_result["value"]
        logger.info(f"📝 Collected {field_name}: {value} for thread: {thread_id}")

        # Check if all required fields are collected
        missing_fields = self._get_missing_fields(thread_id)
        if missing_fields:
            return self._get_next_required_field(thread_id)
        else:
            # All info collected, move to time slot selection
            booking_data["status"] = "selecting_timeslot"
            return self._show_available_timeslots(thread_id)

    def update_booking_info(self, thread_id: str, field_name: str, new_value: str) -> str:
        """
        Update existing booking information

        Args:
            thread_id: Thread identifier
            field_name: Field to update
            new_value: New value

        Returns:
            Confirmation message
        """
        if thread_id not in self.active_bookings:
            return "No active booking session found."

        booking_data = self.active_bookings[thread_id]

        # Validate the new value
        validation_result = self._validate_field(field_name, new_value)
        if not validation_result["valid"]:
            return f"❌ {validation_result['message']}"

        old_value = booking_data["collected_data"].get(field_name, "Not set")
        booking_data["collected_data"][field_name] = validation_result["value"]

        logger.info(f"🔄 Updated {field_name} from '{old_value}' to '{new_value}' for thread: {thread_id}")

        return f"✅ Updated {field_name} from '{old_value}' to '{new_value}'. Current booking details:\n{self._format_booking_summary(thread_id)}"

    def _validate_field(self, field_name: str, value: str) -> Dict[str, Any]:
        """Validate field value based on type"""
        if field_name not in self.booking_requirements:
            return {"valid": False, "message": f"Unknown field: {field_name}"}

        field_config = self.booking_requirements[field_name]
        field_type = field_config["type"]

        # Basic validation
        if not value or not value.strip():
            return {"valid": False, "message": "Value cannot be empty"}

        value = value.strip()

        if field_type == "email":
            if "@" not in value or "." not in value:
                return {"valid": False, "message": "Please provide a valid email address"}
        elif field_type == "phone":
            # Remove common phone formatting
            clean_phone = value.replace("-", "").replace(" ", "").replace("(", "").replace(")", "")
            if len(clean_phone) < 10:
                return {"valid": False, "message": "Please provide a valid phone number (at least 10 digits)"}

        return {"valid": True, "value": value, "message": "Valid"}

    def _get_missing_fields(self, thread_id: str) -> List[str]:
        """Get list of missing required fields"""
        booking_data = self.active_bookings[thread_id]
        collected = booking_data["collected_data"]

        missing = []
        for field_name, config in self.booking_requirements.items():
            if config["required"] and field_name not in collected:
                missing.append(field_name)

        return missing

    def _get_next_required_field(self, thread_id: str) -> str:
        """Get the next required field to collect"""
        missing_fields = self._get_missing_fields(thread_id)

        if not missing_fields:
            return "All required information collected!"

        next_field = missing_fields[0]
        field_config = self.booking_requirements[next_field]

        # Show current progress
        booking_data = self.active_bookings[thread_id]
        collected_count = len(booking_data["collected_data"])
        total_count = len([f for f, c in self.booking_requirements.items() if c["required"]])

        progress = f"📋 Booking Information ({collected_count}/{total_count} completed)\n\n"

        if collected_count > 0:
            progress += "✅ Already collected:\n"
            for field, value in booking_data["collected_data"].items():
                field_prompt = self.booking_requirements[field]["prompt"]
                progress += f"   • {field_prompt.title()}: {value}\n"
            progress += "\n"

        progress += f"📝 Next, please provide {field_config['prompt']}:"

        return progress

    def _format_booking_summary(self, thread_id: str) -> str:
        """Format current booking information"""
        booking_data = self.active_bookings[thread_id]
        collected = booking_data["collected_data"]

        summary = "📋 Current Booking Information:\n"
        for field_name, value in collected.items():
            field_prompt = self.booking_requirements[field_name]["prompt"]
            summary += f"   • {field_prompt.title()}: {value}\n"

        missing = self._get_missing_fields(thread_id)
        if missing:
            summary += f"\n❓ Still needed: {', '.join([self.booking_requirements[f]['prompt'] for f in missing])}"

        return summary

    def _show_available_timeslots(self, thread_id: str) -> str:
        """Show available time slots for booking"""
        booking_data = self.active_bookings[thread_id]
        collected = booking_data["collected_data"]

        # Get course/product specific slots if needed
        course_product = collected.get("course_product", "General")

        # Filter available slots
        available_slots = [slot for slot in self.available_slots if slot["available"]][:10]  # Show first 10

        if not available_slots:
            return "❌ No available time slots at the moment. Please try again later."

        response = f"""✅ All information collected! Here's your booking summary:
{self._format_booking_summary(thread_id)}

📅 Available Time Slots for {course_product}:

"""

        # Group slots by date
        slots_by_date = {}
        for slot in available_slots:
            date = slot["date"]
            if date not in slots_by_date:
                slots_by_date[date] = []
            slots_by_date[date].append(slot)

        slot_number = 1
        booking_data["available_slots"] = {}  # Store for selection

        for date, slots in sorted(slots_by_date.items()):
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            readable_date = date_obj.strftime("%A, %B %d, %Y")
            response += f"\n📅 {readable_date}:\n"

            for slot in slots:
                booking_data["available_slots"][str(slot_number)] = slot
                response += f"   {slot_number}. {slot['time']} - {slot['service_type']}\n"
                slot_number += 1

        response += f"\n💡 Please reply with the slot number (1-{slot_number-1}) to book your appointment."

        return response

    def select_timeslot(self, thread_id: str, slot_number: str) -> str:
        """Select a time slot and confirm booking"""
        if thread_id not in self.active_bookings:
            return "No active booking session found."

        booking_data = self.active_bookings[thread_id]

        if booking_data.get("status") != "selecting_timeslot":
            return "Please complete the booking information first."

        available_slots = booking_data.get("available_slots", {})

        if slot_number not in available_slots:
            return f"❌ Invalid slot number. Please choose a number between 1 and {len(available_slots)}."

        selected_slot = available_slots[slot_number]

        # Create the booking
        booking_result = self._create_booking(thread_id, selected_slot)

        return booking_result

    def _create_booking(self, thread_id: str, selected_slot: Dict) -> str:
        """Create the final booking"""
        booking_data = self.active_bookings[thread_id]
        collected = booking_data["collected_data"]

        # Check if slot is still available
        slot_still_available = False
        for slot in self.available_slots:
            if (slot["date"] == selected_slot["date"] and
                slot["time"] == selected_slot["time"] and
                slot["service_type"] == selected_slot["service_type"] and
                slot["available"]):
                # Mark slot as booked
                slot["available"] = False
                slot_still_available = True
                break

        if not slot_still_available:
            return "❌ Sorry, this time slot is no longer available. Please select another slot."

        # Create booking record
        booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        booking_record = {
            "booking_id": booking_id,
            "thread_id": thread_id,
            "name": collected["name"],
            "course_product": collected["course_product"],
            "email": collected["email"],
            "phone": collected["phone"],
            "date": selected_slot["date"],
            "time": selected_slot["time"],
            "service_type": selected_slot["service_type"],
            "status": "confirmed",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        self.booked_appointments.append(booking_record)

        # Update booking session
        booking_data["status"] = "completed"
        booking_data["booking_id"] = booking_id
        booking_data["booking_record"] = booking_record

        # Format confirmation
        date_obj = datetime.strptime(selected_slot["date"], "%Y-%m-%d")
        readable_date = date_obj.strftime("%A, %B %d, %Y")

        confirmation = f"""
🎉 Booking Confirmed Successfully!

📋 Booking Details:
• Booking ID: {booking_id}
• Name: {collected['name']}
• Course/Product: {collected['course_product']}
• Email: {collected['email']}
• Phone: {collected['phone']}
• Date: {readable_date}
• Time: {selected_slot['time']}
• Service: {selected_slot['service_type']}

📧 A confirmation email will be sent to {collected['email']}.
📱 You'll receive an SMS reminder 24 hours before your appointment.

💡 To modify or cancel this booking, please contact us with your Booking ID: {booking_id}

Thank you for choosing our services! 🙏
"""

        logger.info(f"✅ Booking created successfully: {booking_id} for thread: {thread_id}")

        return confirmation.strip()

    def _show_existing_booking(self, thread_id: str) -> str:
        """Show existing booking details"""
        booking_data = self.active_bookings[thread_id]
        booking_record = booking_data.get("booking_record")

        if not booking_record:
            return "No booking record found."

        date_obj = datetime.strptime(booking_record["date"], "%Y-%m-%d")
        readable_date = date_obj.strftime("%A, %B %d, %Y")

        return f"""
📋 Your Existing Booking:

• Booking ID: {booking_record['booking_id']}
• Name: {booking_record['name']}
• Course/Product: {booking_record['course_product']}
• Email: {booking_record['email']}
• Phone: {booking_record['phone']}
• Date: {readable_date}
• Time: {booking_record['time']}
• Service: {booking_record['service_type']}
• Status: {booking_record['status'].title()}

💡 To modify this booking, please let me know what you'd like to change.
"""

    def _continue_booking_process(self, thread_id: str) -> str:
        """Continue an existing booking process"""
        booking_data = self.active_bookings[thread_id]
        status = booking_data.get("status")

        if status == "collecting_info":
            return self._get_next_required_field(thread_id)
        elif status == "selecting_timeslot":
            return self._show_available_timeslots(thread_id)
        else:
            return "Unknown booking status. Please start a new booking."
    
    def _generate_mock_slots(self) -> List[Dict]:
        """Generate mock available time slots for the next 30 days"""
        slots = []
        start_date = datetime.now().date()
        
        # Generate slots for next 30 days
        for day_offset in range(1, 31):
            date = start_date + timedelta(days=day_offset)
            
            # Skip weekends for this example
            if date.weekday() >= 5:
                continue
            
            # Generate time slots (9 AM to 5 PM)
            for hour in range(9, 17):
                # Randomly make some slots unavailable
                if random.random() > 0.3:  # 70% availability
                    slots.append({
                        "date": date.strftime("%Y-%m-%d"),
                        "time": f"{hour:02d}:00",
                        "service_type": "General Consultation",
                        "available": True
                    })
                    
                    # Add some specialized slots
                    if random.random() > 0.7:  # 30% chance
                        slots.append({
                            "date": date.strftime("%Y-%m-%d"),
                            "time": f"{hour:02d}:30",
                            "service_type": "IELTS Consultation",
                            "available": True
                        })
        
        return slots
    
    def get_current_date(self) -> str:
        """
        Get the current date and time
        
        Returns:
            Formatted current date and time
        """
        now = datetime.now()
        formatted_date = now.strftime("%A, %B %d, %Y at %I:%M %p")
        
        logger.info(f"📅 Current date requested: {formatted_date}")
        
        return f"Today is {formatted_date}"
    
    def get_available_slots(self, service_type: str = "any", days_ahead: int = 14) -> str:
        """
        Get available appointment slots
        
        Args:
            service_type: Type of service ("any", "General Consultation", "IELTS Consultation")
            days_ahead: Number of days ahead to search
            
        Returns:
            Formatted string of available slots
        """
        logger.info(f"📅 Getting available slots for {service_type}, {days_ahead} days ahead")
        
        try:
            # Filter slots based on criteria
            end_date = (datetime.now().date() + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
            
            filtered_slots = []
            for slot in self.available_slots:
                if not slot["available"]:
                    continue
                    
                if slot["date"] > end_date:
                    continue
                
                if service_type != "any" and slot["service_type"] != service_type:
                    continue
                
                filtered_slots.append(slot)
            
            if not filtered_slots:
                return f"No available slots found for {service_type} in the next {days_ahead} days. Please try a different service type or extend the search period."
            
            # Group by date
            slots_by_date = {}
            for slot in filtered_slots[:20]:  # Limit to first 20 slots
                date = slot["date"]
                if date not in slots_by_date:
                    slots_by_date[date] = []
                slots_by_date[date].append(slot)
            
            # Format response
            response_parts = [f"Available slots for {service_type}:\n"]
            
            for date, slots in sorted(slots_by_date.items()):
                # Convert date to readable format
                date_obj = datetime.strptime(date, "%Y-%m-%d")
                readable_date = date_obj.strftime("%A, %B %d, %Y")
                
                response_parts.append(f"\n📅 {readable_date}:")
                for slot in slots:
                    response_parts.append(f"  • {slot['time']} - {slot['service_type']}")
            
            response_parts.append(f"\nTo book an appointment, please provide your name, email, phone number, preferred date, and time.")
            
            return "".join(response_parts)
            
        except Exception as e:
            error_msg = f"Error retrieving available slots: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def book_appointment(self, name: str, email: str, phone: str, 
                        date: str, time: str, service_type: str = "General Consultation") -> str:
        """
        Book an appointment
        
        Args:
            name: Customer name
            email: Customer email
            phone: Customer phone number
            date: Appointment date (YYYY-MM-DD format)
            time: Appointment time (HH:MM format)
            service_type: Type of service
            
        Returns:
            Booking confirmation or error message
        """
        logger.info(f"📅 Booking appointment for {name} on {date} at {time}")
        
        try:
            # Validate required fields
            if not all([name, email, phone, date, time]):
                return "Please provide all required information: name, email, phone, date, and time."
            
            # Validate email format (basic)
            if "@" not in email or "." not in email:
                return "Please provide a valid email address."
            
            # Validate phone format (basic)
            if len(phone.replace("-", "").replace(" ", "")) < 10:
                return "Please provide a valid phone number."
            
            # Check if slot is available
            slot_found = False
            for slot in self.available_slots:
                if (slot["date"] == date and 
                    slot["time"] == time and 
                    slot["service_type"] == service_type and 
                    slot["available"]):
                    
                    # Mark slot as booked
                    slot["available"] = False
                    slot_found = True
                    break
            
            if not slot_found:
                return f"Sorry, the requested slot ({date} at {time} for {service_type}) is not available. Please check available slots and try again."
            
            # Create booking record
            booking = {
                "booking_id": f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}",
                "name": name,
                "email": email,
                "phone": phone,
                "date": date,
                "time": time,
                "service_type": service_type,
                "status": "confirmed",
                "created_at": datetime.now().isoformat()
            }
            
            self.booked_appointments.append(booking)
            
            # Format confirmation
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            readable_date = date_obj.strftime("%A, %B %d, %Y")
            
            confirmation = f"""
✅ Appointment Confirmed!

Booking Details:
• Booking ID: {booking['booking_id']}
• Name: {name}
• Email: {email}
• Phone: {phone}
• Date: {readable_date}
• Time: {time}
• Service: {service_type}

A confirmation email will be sent to {email}. Please arrive 10 minutes early for your appointment.

If you need to reschedule or cancel, please contact us with your booking ID: {booking['booking_id']}
"""
            
            logger.info(f"✅ Appointment booked successfully: {booking['booking_id']}")
            return confirmation.strip()
            
        except Exception as e:
            error_msg = f"Error booking appointment: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def get_booking_stats(self) -> Dict:
        """Get booking statistics for debugging"""
        total_slots = len(self.available_slots)
        available_slots = len([s for s in self.available_slots if s["available"]])
        booked_slots = total_slots - available_slots
        
        return {
            "total_slots": total_slots,
            "available_slots": available_slots,
            "booked_slots": booked_slots,
            "total_bookings": len(self.booked_appointments)
        }
